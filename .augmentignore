# Node.js dependencies
node_modules/

# Build artifacts
build/
dist/
out/

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
temp/

# Package manager files
.npm/
.yarn/
.pnp.*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage reports
coverage/
.nyc_output/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock
