package io.wyden.target.registry.statemachine;

import io.micrometer.common.util.StringUtils;
import io.wyden.target.registry.k8s.DockerImagesConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
public class StatePromotionValidator {
    private final DockerImagesConfiguration dockerImagesConfig;
    private Logger LOGGER = LoggerFactory.getLogger(StatePromotionValidator.class);

    public StatePromotionValidator(DockerImagesConfiguration dockerImagesConfig) {
        this.dockerImagesConfig = dockerImagesConfig;
    }

    public void validateThatAccountIsNotE2E(String venueAccountId) {
        if (venueAccountId.contains("e2e")) {
            String message = String.format("Operation on connector skipped, it is e2e venue account %s", venueAccountId);
            throw new RuntimeException(message);
        }
    }

    void validateSuspendedAt(String venueAccountId, String suspendedAt) {
        if (StringUtils.isNotBlank(suspendedAt)) {
            String message = String.format("Creating/updating connector skipped, it is not active venue account: %s", venueAccountId);
            throw new RuntimeException(message);
        }
    }

    void validateDockerImage(String venueAccountId, String venueName) {
        String dockerImage = dockerImagesConfig.getDockerImage(venueName);
        if (isBlank(dockerImage)) {
            String message = String.format("Creating/updating connector skipped, unable to parse docker image for venue account %s, venue %s", venueAccountId, venueName);
            throw new RuntimeException(message);
        }
    }

    void validateParameters(String venueAccountId, int parameterCount) {
        if (parameterCount < 1) {
            String message = String.format("Creating/updating connector skipped, empty parameters list for venue account %s", venueAccountId);
            throw new RuntimeException(message);
        }
    }

}