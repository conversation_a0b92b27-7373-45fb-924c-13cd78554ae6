package io.wyden.target.registry.statemachine.state;

import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.published.targetregistry.ConnectorState;
import io.wyden.target.registry.domain.model.ConnectorStatus;
import io.wyden.target.registry.statemachine.DiagnosticEventExpireService;
import io.wyden.target.registry.statemachine.DiagnosticEventsReceiveService;
import io.wyden.target.registry.statemachine.TargetState;
import io.wyden.target.registry.statemachine.TargetStateContext;
import io.wyden.target.registry.statemachine.UpdateConnectorService;

// State set by reconciliation when connector vault data cannot be found
public class TargetStateUndefined extends TargetState {

    public TargetStateUndefined(TargetStateContext context) {
        super(context);
    }

    @Override
    public ConnectorStatus getStatus() {
        return ConnectorStatus.UNDEFINED;
    }

    @Override
    public String getMessage() {
        return "No connector capability information";
    }

    @Override
    public TargetState onCreateOrUpdate(ConnectorRequest request, UpdateConnectorService service) {
        service.update(request);
        return new TargetStateDeployed(context);
    }

    @Override
    public TargetState onDiagnosticEvent(ConnectorState newState, TargetState prevState, String connectorId, String venue, DiagnosticEventsReceiveService service) {
        // This is for connector-wrapper-mock where connectors are not under target-registry control but still need diagnostic events
        service.accept(newState, connectorId, venue, prevState);
        return new TargetStateUndefined(context);
    }

    @Override
    public TargetState onExpire(String connectorId, DiagnosticEventExpireService service) {
        // This is for connector-wrapper-mock where connectors are not under target-registry control but still need diagnostic events
        // We don't care if connector expires
        return new TargetStateUndefined(context);
    }


}
